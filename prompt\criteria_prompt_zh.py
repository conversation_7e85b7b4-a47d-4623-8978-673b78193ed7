# 最终生成文章质量的四个评判维度
# 1、全面性（Comprehensiveness）：文章确保覆盖相关领域的整体信息，不遗漏关键性知识
# 2、洞察力（Insight）/内容深度（Depth）：文章深入分析相关要素的因果关系和作用机制，提供有参考性的见解
# 3、指令遵循能力（Instruction-Following）/相关性（Relevance）：文章紧扣调研主题，避免过度发散
# 4、可读性（Readability）：文章语言规范，结构清晰，易于理解

# criteria生成pipeline
# step1、根据给定prompt，让模型给出上述四个维度：全面性、洞察力、指令遵循能力、可读性分别应该占总评判标准的百分之多少，json文件输出
# step2、对每个维度，让模型根据prompt生成具体的评判标准，并给出每条评判标准的权重，json文件输出 (当前实现只生成标准，未要求权重)

# step1 prompt
generate_eval_dimension_weight_prompt = """
<system_role>
你是一名经验丰富的调研文章评估专家。你擅长根据具体的调研任务，深刻理解其目标、挑战与核心价值点，并据此为后续的文章质量评估设定**动态、合理、且有充分理由支撑**的维度权重。
</system_role>

<user_prompt>
现在有一个深度调研任务，内容如下：
<task>
"{task_prompt}"
</task>

<instruction>
**背景**：调研团队将根据上述 `<task>` 进行深度且全面的调研，并最终产出一篇高质量的调研文章。
**你的任务**：作为评估专家，你需要为我们的评估团队设定针对该特定 `<task>` 的评估标准权重。评估将围绕以下四个维度进行：
1.  **全面性 (Comprehensiveness):** 信息覆盖的广度、深度和相关性。
2.  **洞察力 (Insight):** 分析的深度、独到性、逻辑性和结论价值。
3.  **指令遵循能力 (Instruction Following):** 报告是否准确、完整地回应了任务的所有要求和限定条件。
4.  **可读性 (Readability):** 结构清晰度、语言流畅度、数据呈现效果和整体易理解性。

**评估公式**：总评分 = 全面性 * 全面性权重 + 洞察力 * 洞察力权重 + 指令遵循能力 * 指令遵循能力权重 + 可读性 * 可读性权重。（**注意：所有权重之和必须精确等于 1.0**）

**核心要求**：
1.  **深入分析任务**：请仔细研究 `<task>` 的具体内容、隐含的目标、潜在的难点以及该任务成果的核心价值所在。
2.  **动态分配权重**：基于你的分析，为上述四个维度分配权重（使用0到1之间的小数表示，如 0.3）。**关键在于理解不同任务的侧重点不同，权重必须依据任务特性灵活调整，绝非固定不变。**
3.  **阐述分配理由**：你的分析 (`<analysis>`) **必须清晰、具体地解释为什么给予每个维度特定的权重**，并**直接将理由与 <task>的要求和特性紧密联系起来**。这是评估你工作质量的关键。
4.  **标准格式输出**：严格按照下面示例的格式，先输出包含详细理由的 `<analysis>` 文本，然后紧接着提供包含权重分配结果的 `<json_output>` 。

</instruction>

<examples_rationale>
以下提供两个示例，旨在演示**如何根据任务性质的变化来调整评估维度权重并阐述理由**。请重点学习这两个示例中的**思考逻辑和分析方法**，而不是简单模仿其内容或权重数值。
</examples_rationale>

<example_1>
<task>
"预测佛山的房价走势，二手房是出售好还是出租好？"
</task>
<output>
<analysis>
此任务核心是基于预测提供明确的决策建议（卖或租），价值在于结论的指导性和准确性。因此，评估侧重于分析深度和问题回应的精准度。
* **洞察力 (Insight, 0.4):** 任务要求预测未来并做出选择，深刻的分析、判断逻辑和结论的价值是决定报告质量的关键，故权重最高。
* **指令遵循能力 (Instruction Following, 0.25):** 任务包含两个明确问题（预测+建议）和特定范围（佛山二手房），报告必须精准回应这两个问题才能有效指导决策，故权重较高。
* **全面性 (Comprehensiveness, 0.2):** 需要全面的数据（市场、政策、经济等）作为分析基础，但相比于如何运用数据得出结论，其本身权重次之。
* **可读性 (Readability, 0.15):** 清晰传达复杂的分析和建议是必要的，但在内容深度和针对性面前，其权重相对较低。
</analysis>
<json_output>
{{
    "comprehensiveness": 0.2,
    "insight": 0.4,
    "instruction_following": 0.25,
    "readability": 0.15
}}
</json_output>
</output>
</example_1>

<example_2>
<task>
"为我调研过去一百年，各类大资产的回报率都是什么样的？"
</task>
<output>
<analysis>
此任务的核心目标是提供一个全面、准确、跨越百年历史的主要资产回报率概览，重点在于信息的广度、历史跨度和数据的准确呈现。
* **全面性 (Comprehensiveness, 0.35):** 任务直接要求覆盖"各类"资产和"一百年"数据，信息的广度和完整性是报告价值的基础，故权重最高。
* **可读性 (Readability, 0.25):** 如何将海量、多维度的历史数据清晰、直观地呈现出来，便于用户理解和比较，是此任务的一大挑战和关键成功要素，故权重很高。
* **洞察力 (Insight, 0.2):** 在数据呈现基础上进行总结分析（如实际回报率、风险特征）能提升价值，但并非任务首要目标，权重较低。
* **指令遵循能力 (Instruction Following, 0.2):** 保证报告内容紧扣"各类资产"、"一百年"、"回报率"是基本要求，相对权重较低。
</analysis>
<json_output>
{{
    "comprehensiveness": 0.35,
    "insight": 0.2,
    "instruction_following": 0.2,
    "readability": 0.25
}}
</json_output>
</output>
</example_2>

请严格遵循以上说明和方法，现在针对以下具体任务开始你的工作：
<task>
"{task_prompt}"
</task>
请输出你的 `<analysis>` 和 `<json_output>`。
</user_prompt>
"""


# step2 prompt: 根据给定任务，让模型生成具体评判标准并分配权重

# 维度：全面性 (Comprehensiveness)
generate_eval_criteria_prompt_comp = """
<system_role>
你是一名经验丰富的调研文章评估专家。你擅长将抽象的评估维度（如"全面性"）分解为针对具体调研任务的、可操作的、清晰的评判细则，并为每条细则分配合理的权重和解释理由。
</system_role>

<user_prompt>
**背景**：我们正在从四个维度评估一篇针对以下调研任务撰写的深度调研文章，分别是：全面性、洞察力、指令遵循能力、可读性。
1.  **全面性 (Comprehensiveness):** 信息覆盖的广度、深度和相关性。
2.  **洞察力 (Insight):** 分析的深度、独到性、逻辑性和结论价值。
3.  **指令遵循能力 (Instruction Following):** 报告是否准确、完整地回应了任务的所有要求和限定条件。
4.  **可读性 (Readability):** 结构清晰度、语言流畅度、数据呈现效果和整体易理解性。

<task>
"{task_prompt}"
</task>

<instruction>
**你的目标**：为这篇调研文章的 **全面性 (Comprehensiveness)** 维度，制定一套详细、具体、且高度针对上述 `<task>` 的评判标准。你需要：
1.  **分析任务**: 深入分析 `<task>`，识别出要做到"全面"，必须覆盖的关键信息领域、角度和深度。
2.  **制定标准**: 基于分析，提出具体的评判标准细则。
3.  **解释理由**: 为每条标准提供简短的解释 (`explanation`)，说明它为何对于评估此 `<task>` 的全面性是重要的。
4.  **分配权重**: 为每条标准分配合理的权重 (`weight`)，确保所有标准的权重之和精确等于 **1.0**。权重应反映该标准对于达成任务全面性目标的相对重要性。
5.  **避免重叠**: 明确当前正在制定与**全面性**维度相关的评判标准，避免评判标准涉及到其他的评判维度：洞察力、指令遵循能力、可读性维度。

**核心要求**：
1.  **紧扣任务**：分析、标准、解释和权重都必须直接关联 `<task>` 的核心要求和特性。
2.  **理由充分**: `<analysis>` 部分需清晰阐述制定这些标准和权重的总体思路，并与 `<task>` 联系。`explanation` 需解释单条标准的针对性。
3.  **标准多样性**: 标准尽量不重叠，需要尽可能覆盖全面性维度内的所有方面，避免遗漏。
4.  **权重合理**: 权重分配需有逻辑，反映各项在全面性维度内的相对重要程度。
5.  **标准格式输出**：严格按照下面示例的格式，先输出 `<analysis>` 文本，然后紧接着提供 `<json_output>`。
</instruction>

<example_rational>
以下提供示例，旨在演示**如何根据任务要求制定全面性评判标准**。请重点学习这个示例中的**思考逻辑和分析方法**，而不是简单模仿其内容或权重数值。
</example_rational>

<example>
<task>
"预测佛山的房价走势，二手房是出售好还是出租好？"
</task>

<output>
<analysis>
为了全面评估针对"预测佛山房价走势，二手房出售或出租决策"这一任务的调研文章，需要从多个维度进行考量。该任务具有双重目标：一是进行市场趋势预测，二是基于预测进行具体的投资决策建议。因此，全面性评估必须确保文章覆盖了影响房价的关键宏观与微观因素，并对出售与出租两种选择进行了充分的比较分析。

具体而言，评估标准需要涵盖：
1.  **宏观与政策环境**：全国性及地方性的经济、货币、房地产政策是影响房价的基础背景。
2.  **佛山本地市场基本面**：城市规划、产业发展、人口流动、土地供应、新房/二手房库存与交易量等本地化因素直接决定了佛山市场的具体表现。
3.  **房价趋势预测的依据与逻辑**：不仅要给出预测结论，更要展示基于数据的分析过程和严谨的逻辑链条，区分短期与长期趋势。
4.  **出售选择的分析**：需要量化潜在收益（考虑税费、交易成本）和风险。
5.  **出租选择的分析**：需要评估租金回报率、持有成本（物业费、维修、空置期）、管理复杂性等。
6.  **比较与决策框架**：必须将出售与出租放在同一框架下，结合预测的房价走势，进行全面的利弊权衡，并考虑不同房主（如投资目的、现金流需求、风险偏好）的适用性。

权重分配倾向于平均分配给趋势预测部分（宏观、本地、预测本身）和决策比较部分（出售分析、出租分析、综合比较），因为两者对完成任务都至关重要。其中，本地市场基本面分析和量化的租售比较被赋予略高权重，因为它们是形成针对性结论的关键。
</analysis>
<json_output>
[
  {{
    "criterion": "宏观经济与政策环境分析",
    "explanation": "评估文章是否考察了影响全国及佛山房地产市场的宏观经济指标（如GDP增长、利率、通胀）和关键政策（如限购、信贷政策、城市发展规划），这些是判断大趋势的基础。",
    "weight": 0.15
  }},
  {{
    "criterion": "佛山本地市场基本面深度分析",
    "explanation": "评估文章是否深入分析了佛山特有的市场供需状况、人口流入/流出、产业结构、交通基建发展、新房与二手房库存及交易数据等，这些是预测本地市场走势的核心依据。",
    "weight": 0.20
  }},
  {{
    "criterion": "房价走势预测与论证 ",
    "explanation": "评估文章是否基于上述分析，明确提出了对佛山未来（需区分短期/长期）房价走势的预测，并提供了清晰、有数据支持的论证逻辑。",
    "weight": 0.15
  }},
  {{
    "criterion": "出售方案的量化与质性分析",
    "explanation": "评估文章是否全面分析了出售二手房的利弊，包括潜在售价、交易成本、税费、变现速度，以及市场时机风险等。",
    "weight": 0.15
  }},
  {{
    "criterion": "出租方案的量化与质性分析",
    "explanation": "评估文章是否全面分析了出租二手房的利弊，包括预期租金收入、空置期风险、持有成本（物业、维修、税费）、管理投入、以及长期持有增值/贬值可能性。",
    "weight": 0.20
  }},
  {{
    "criterion": "租售决策比较与建议的针对性",
    "explanation": "评估文章是否结合房价走势预测，系统地比较了出售和出租两种方案的财务回报与风险，并能根据不同的房主情况（如资金需求、风险承受能力、投资周期）提供有针对性的、情景化的决策建议。",
    "weight": 0.15
  }}
]
</json_output>
</output>
</example>

请严格遵循以上说明和方法，现在针对以下具体任务开始你的工作：
<task>
"{task_prompt}"
</task>
请输出你的 `<analysis>` 和 `<json_output>`。
</user_prompt>
"""

# 维度：洞察力 (Insight) / 内容深度 (Depth)
generate_eval_criteria_prompt_insight = """
<system_role>
你是一名经验丰富的调研文章评估专家。你擅长将抽象的评估维度（如"洞察力"）分解为针对具体调研任务的、可操作的、清晰的评判细则，并为每条细则分配合理的权重和解释理由。
</system_role>

<user_prompt>
**背景**：我们正在从四个维度评估一篇针对以下调研任务撰写的深度调研文章，分别是：全面性、洞察力、指令遵循能力、可读性。
1.  **全面性 (Comprehensiveness):** 信息覆盖的广度、深度和相关性。
2.  **洞察力 (Insight):** 分析的深度、独到性、逻辑性和结论价值。
3.  **指令遵循能力 (Instruction Following):** 报告是否准确、完整地回应了任务的所有要求和限定条件。
4.  **可读性 (Readability):** 结构清晰度、语言流畅度、数据呈现效果和整体易理解性。

<task>
"{task_prompt}"
</task>

<instruction>
**你的目标**：为这篇调研文章的 **洞察力 (Insight)** 维度，制定一套详细、具体、且高度针对上述 `<task>` 的评判标准。你需要：
1.  **分析任务**: 深入分析 `<task>`，识别出要展现"洞察力"，需要在哪些方面进行深入分析、逻辑推演、观点提炼或价值判断。
2.  **制定标准**: 基于分析，提出具体的评判标准细则，着重于分析的深度、逻辑性、原创性和结论价值。
3.  **解释理由**: 为每条标准提供简短的解释 (`explanation`)，说明它为何对于评估此 `<task>` 的洞察力是重要的。
4.  **分配权重**: 为每条标准分配合理的权重 (`weight`)，确保所有标准的权重之和精确等于 **1.0**。权重应反映该标准对于体现任务洞察力目标的相对重要性。
5.  **避免重叠**: 明确当前正在制定与**洞察力**维度相关的评判标准，避免评判标准涉及到其他的评判维度：全面性、指令遵循能力、可读性维度。

**核心要求**：
1.  **紧扣任务**：分析、标准、解释和权重都必须直接关联 `<task>` 的核心要求和特性。
2.  **超越表面**：标准应评估分析的深度、逻辑严谨性、见解的独到性和结论的价值，而非仅仅是信息的罗列。
3.  **理由充分**: `<analysis>` 部分需清晰阐述制定这些标准和权重的总体思路，并与 `<task>` 联系。`explanation` 需解释单条标准的针对性。
4.  **权重合理**: 权重分配需有逻辑，反映各项在洞察力维度内的相对重要程度。
5.  **标准格式输出**：严格按照下面示例的格式，先输出 `<analysis>` 文本，然后紧接着提供 `<json_output>`。
</instruction>

<example_rational>
以下提供示例，旨在演示**如何根据任务要求制定洞察力评判标准**。请重点学习这个示例中的**思考逻辑和分析方法**，而不是简单模仿其内容或权重数值。
</example_rational>

<example>
<task>
"预测佛山的房价走势，二手房是出售好还是出租好？"
</task>

<output>
<analysis>
为了评估针对“预测佛山房价走势及二手房租售决策”任务报告的洞察力，需要关注其分析的深度、逻辑性和结论的价值，超越简单信息呈现。该任务的核心在于基于复杂因素进行预测和提供决策支持，因此洞察力体现在能否揭示关键驱动因素、建立严谨的逻辑链条、进行多维度的风险收益权衡，并提出具有前瞻性和针对性的见解。

评估标准需要侧重于：
1.  **核心驱动因素识别与剖析**：不仅要列出因素，更要分析其作用机制和相对重要性。
2.  **预测逻辑的严密性**：评估预测过程是否基于数据和合理假设，逻辑链条是否清晰、可信。
3.  **因素间互动关系的理解**：能否揭示宏观、本地、政策等因素如何相互影响房价。
4.  **租售比较框架的深度**：是否构建了超越表面收益计算的、包含风险、时间价值、流动性等多维度的比较框架。
5.  **情景化与批判性思维**：能否考虑不同房主情况或市场变化，并对普遍观点进行审视。
6.  **见解的独到性和价值**：是否提出了超越常识、具有启发性或实际指导意义的观点。

权重分配倾向于给予核心驱动因素分析和预测逻辑较高的权重，因为它们是洞察力的基础。同时，租售比较框架的深度和批判性思维也占有重要比重，体现了超越数据罗列的分析能力。提供独特见解虽然分值不高，但是区分优秀报告的关键。
</analysis>
<json_output>
[
  {{
    "criterion": "核心驱动因素识别与深度剖析",
    "explanation": "评估文章是否不仅识别了影响佛山房价的核心驱动因素（如政策、供需、人口），而且深入分析了它们的作用机制和相对重要性，而非简单罗列。",
    "weight": 0.25
  }},
  {{
    "criterion": "房价走势预测逻辑的严密性与合理性",
    "explanation": "评估文章是否建立了清晰、逻辑严密的预测框架，基于数据和合理假设进行推演，而非仅凭直觉或简单外推。",
    "weight": 0.20
  }},
  {{
    "criterion": "多因素互动机制的揭示",
    "explanation": "评估文章是否深入探讨了宏观经济、本地市场、政策调控等不同因素之间如何相互作用并共同影响佛山房价，展现系统性思考。",
    "weight": 0.15
  }},
  {{
    "criterion": "租售决策比较框架的深度与多维性",
    "explanation": "评估文章是否构建了全面的二手房出售与出租比较框架，超越简单的收益率计算，深入考虑了风险、流动性、时间价值、税费、持有成本等多个维度。",
    "weight": 0.15
  }},
  {{
    "criterion": "分析的情景化与批判性思维",
    "explanation": "评估文章是否考虑了不同房主情况（如资金需求、风险偏好）或特定房产特性下的差异化分析，并对市场主流观点进行了审视或提出了不同角度。",
    "weight": 0.15
  }},
  {{
    "criterion": "见解的独到性与前瞻性价值",
    "explanation": "评估文章是否提供了超越普遍认知、具有启发性或实际指导意义的独特见解或结论，为决策者带来新视角。",
    "weight": 0.10
  }}
]
</json_output>
</output>
</example>

请严格遵循以上说明和方法，现在针对以下具体任务开始你的工作：
<task>
"{task_prompt}"
</task>
请输出你的 `<analysis>` 和 `<json_output>`。
</user_prompt>
"""

# 维度：指令遵循能力 (Instruction-Following) / 相关性 (Relevance)
generate_eval_criteria_prompt_Inst = """
<system_role>
你是一名经验丰富的调研文章评估专家。你擅长将抽象的评估维度（如"指令遵循能力"）分解为针对具体调研任务的、可操作的、清晰的评判细则，并为每条细则分配合理的权重和解释理由。
</system_role>

<user_prompt>
**背景**：我们正在从四个维度评估一篇针对以下调研任务撰写的深度调研文章，分别是：全面性、洞察力、指令遵循能力、可读性。
1.  **全面性 (Comprehensiveness):** 信息覆盖的广度、深度和相关性。
2.  **洞察力 (Insight):** 分析的深度、独到性、逻辑性和结论价值。
3.  **指令遵循能力 (Instruction Following):** 报告是否准确、完整地回应了任务的所有要求和限定条件。
4.  **可读性 (Readability):** 结构清晰度、语言流畅度、数据呈现效果和整体易理解性。

<task>
"{task_prompt}"
</task>

<instruction>
**你的目标**：为这篇调研文章的 **指令遵循能力 (Instruction Following)** 维度，制定一套详细、具体、且高度针对上述 `<task>` 的评判标准。你需要：
1.  **分析任务**: 深入分析 `<task>` 中的具体指令、问题、范围限定（如地域、时间、对象等）和核心目标。
2.  **制定标准**: 基于分析，提出具体的评判标准细则，着重于文章是否准确、完整、直接地回应了所有任务指令，内容是否严格遵守了限定条件，是否紧扣主题。
3.  **解释理由**: 为每条标准提供简短的解释 (`explanation`)，说明它为何对于评估此 `<task>` 的指令遵循度是重要的。
4.  **分配权重**: 为每条标准分配合理的权重 (`weight`)，确保所有标准的权重之和精确等于 **1.0**。权重应反映该标准对于确保任务被准确、切题地完成的相对重要性。
5.  **避免重叠**: 明确当前正在制定与**指令遵循能力**维度相关的评判标准，避免评判标准涉及到其他的评判维度：全面性、洞察力、可读性维度。

**核心要求**：
1.  **紧扣指令**：分析、标准、解释和权重都必须直接对应 `<task>` 的明确要求、问题和限定条件。
2.  **聚焦响应度与相关性**：标准应评估内容是否切题、范围是否准确、所有问题是否被直接且完整地回答。
3.  **理由充分**: `<analysis>` 部分需清晰阐述制定这些标准和权重的总体思路，并与 `<task>` 联系。`explanation` 需解释单条标准的针对性。
4.  **权重合理**: 权重分配需有逻辑，反映各项指令或限定条件在任务中的相对重要程度。
5.  **标准格式输出**：严格按照下面示例的格式，先输出 `<analysis>` 文本，然后紧接着提供 `<json_output>`。
</instruction>

<example_rational>
以下提供示例，旨在演示**如何根据任务要求制定指令遵循能力评判标准**。请重点学习这个示例中的**思考逻辑和分析方法**，而不是简单模仿其内容或权重数值。
</example_rational>

<example>
<task>
"预测佛山的房价走势，二手房是出售好还是出租好？"
</task>

<output>
<analysis>
评估针对“预测佛山房价走势及二手房租售决策”任务报告的指令遵循能力，关键在于检查其是否精确回应了任务的所有核心要求和隐含限定。该任务包含两个明确的问题（房价预测、租售决策）和两个明确的限定（地点：佛山，房产类型：二手房）。

因此，评估标准必须关注：
1.  **核心问题一的回应**：是否直接、清晰地预测了佛山房价走势。
2.  **核心问题二的回应**：是否直接、明确地对二手房出售或出租给出了建议或判断依据。
3.  **地域限定的遵守**：内容是否严格聚焦于佛山市场。
4.  **房产类型限定的遵守**：内容是否严格聚焦于二手房。
5.  **问题的完整性**：是否同时处理了预测和决策两个方面，没有遗漏。

权重分配上，直接回应两个核心问题最为重要，应占据最大比重，因为它们是任务的根本目标。遵守地域和房产类型限定也至关重要，确保了内容的相关性，权重次之。最后，确保两个问题都得到处理（完整性）也是基本要求。
</analysis>
<json_output>
[
  {{
    "criterion": "佛山房价走势预测的直接性与明确性",
    "explanation": "评估文章是否明确、直接地回应了关于“佛山房价走势预测”的问题，给出了清晰的趋势判断，满足了任务的第一个核心指令。",
    "weight": 0.30
  }},
  {{
    "criterion": "二手房租售决策建议的直接性与明确性",
    "explanation": "评估文章是否明确、直接地回应了“二手房是出售好还是出租好”的问题，提供了清晰的建议、判断依据或比较框架，满足了任务的第二个核心指令。",
    "weight": 0.30
  }},
  {{
    "criterion": "地域范围（佛山）的严格遵守",
    "explanation": "评估文章内容是否严格限定在佛山市场进行分析和讨论，准确遵循了任务的地域限定，避免了无关区域信息的干扰。",
    "weight": 0.15
  }},
  {{
    "criterion": "房产类型（二手房）的严格遵守",
    "explanation": "评估文章内容是否严格聚焦于二手房市场进行分析和决策讨论，准确遵循了任务的房产类型限定，避免了新房或其他类型房产信息的混淆。",
    "weight": 0.15
  }},
  {{
    "criterion": "任务指令的完整覆盖",
    "explanation": "评估文章是否同时包含了对房价走势的预测和对租售决策的分析/建议，完整覆盖了任务提出的所有主要方面，没有遗漏关键部分。",
    "weight": 0.10
  }}
]
</json_output>
</output>
</example>

请严格遵循以上说明和方法，现在针对以下具体任务开始你的工作：
<task>
"{task_prompt}"
</task>
请输出你的 `<analysis>` 和 `<json_output>`。
</user_prompt>
"""

# 维度：可读性 (Readability) - 生成通用标准及典型权重
generate_eval_criteria_prompt_readability = """
<system_role>
你是一名经验丰富的调研文章评估专家。你擅长将抽象的评估维度（如"可读性"）分解为针对具体调研任务的、可操作的、清晰的评判细则，并为每条细则分配合理的权重和解释理由。
</system_role>

<user_prompt>
**背景**：我们正在从四个维度评估一篇针对以下调研任务撰写的深度调研文章，分别是：全面性、洞察力、指令遵循能力、可读性。
1.  **全面性 (Comprehensiveness):** 信息覆盖的广度、深度和相关性。
2.  **洞察力 (Insight):** 分析的深度、独到性、逻辑性和结论价值。
3.  **指令遵循能力 (Instruction Following):** 报告是否准确、完整地回应了任务的所有要求和限定条件。
4.  **可读性 (Readability):** 结构清晰度、语言流畅度、数据呈现效果和整体易理解性。

<task>
"{task_prompt}"
</task>

<instruction>
**你的目标**：为这篇调研文章的 **可读性 (Readability)** 维度，制定一套详细、具体、且相对通用的评判标准，同时考虑 `<task>` 的特点。你需要：
1.  **分析可读性要素**: 思考构成高质量调研报告可读性的关键要素，如结构逻辑、语言表达、信息呈现、格式排版等。
2.  **制定标准**: 基于分析，提出具体的评判标准细则。应覆盖以下方面：
    * 语言清晰度与规范性 (语句、术语、措辞、错误)
    * 内容结构与逻辑 (标题、段落、引言结论、过渡)
    * 信息呈现与密度 (重点、分解、组织方式、冗余度)
    * 数据与可视化 (数据准确清晰、图表使用)
    * 格式与排版 (段落、间距、重点标注)
    * 读者适应性 (术语解释、表达方式)
3.  **解释理由**: 为每条标准提供简短的解释 (`explanation`)，说明它为何对于提升报告的可读性和读者理解是重要的，可结合 `<task>` 类型说明。
4.  **分配权重**: 为每条标准分配合理的权重 (`weight`)，确保所有标准的权重之和精确等于 **1.0**。权重应反映该标准对于整体可读性的相对重要性。
5.  **避免重叠**: 明确当前正在制定与**可读性**维度相关的评判标准，避免评判标准涉及到其他的评判维度：全面性、洞察力、指令遵循能力维度。

**核心要求**：
1.  **覆盖关键要素**: 标准应系统性地覆盖影响可读性的主要方面。
2.  **清晰可操作**: 每条标准应具体、易于理解和判断。
3.  **理由充分**: `<analysis>` 部分需阐述制定这些标准和权重的总体思路。`explanation` 需解释单条标准的重要性。
4.  **权重合理**: 权重分配需有逻辑，反映各项对可读性的相对贡献。
5.  **标准格式输出**：严格按照下面示例的格式，先输出 `<analysis>` 文本，然后紧接着提供 `<json_output>`。
</instruction>

<example_rational>
以下提供示例，旨在演示**如何制定可读性评判标准**。请重点学习这个示例中的**思考逻辑和分析方法**，而不是简单模仿其内容或权重数值。虽然可读性标准有通用性，但权重分配可略微根据任务类型调整。
</example_rational>

<example>
<task>
"预测佛山的房价走势，二手房是出售好还是出租好？"
</task>

<output>
<analysis>
评估针对“预测佛山房价走势及二手房租售决策”任务报告的可读性，需要确保复杂的市场分析和决策比较易于读者理解和吸收。虽然可读性标准具有通用性，但对此类包含预测、数据和决策建议的任务，结构清晰、语言精准、数据呈现直观尤为重要。

评估标准应覆盖：
1.  **结构逻辑性**：文章是否有清晰的框架引导读者理解从市场分析到预测再到决策的全过程。
2.  **语言表达**：语言是否流畅、准确，专业术语使用是否恰当并有解释。
3.  **段落组织**：段落是否聚焦、过渡是否自然，便于跟上论证思路。
4.  **信息呈现**：关键信息是否突出，数据是否清晰呈现，是否适当使用图表。
5.  **格式排版**：整体版面是否整洁，有助于长时间阅读。

权重分配上，结构清晰度应获得最高权重，因为它是理解复杂分析的基础。语言表达和专业术语使用的准确性也至关重要，直接影响信息传递的效率和准确性。数据和图表的呈现方式对于此类数据密集型任务的可读性贡献也较大。其余方面如段落组织、格式排版等作为辅助，共同构成良好的阅读体验。
</analysis>
<json_output>
[
  {{
    "criterion": "文章整体结构与逻辑清晰度",
    "explanation": "评估文章是否具有清晰的逻辑结构（如引言、市场分析、趋势预测、租售比较、结论），标题层级分明，能有效引导读者理解复杂的分析过程。",
    "weight": 0.20
  }},
  {{
    "criterion": "语言表达的流畅性、准确性与专业性",
    "explanation": "评估语言是否流畅自然，无明显语病、错别字，用词准确；专业术语（如租售比、回报率）使用恰当，必要时有解释。",
    "weight": 0.20
  }},
  {{
    "criterion": "段落组织的连贯性与聚焦性",
    "explanation": "评估段落是否围绕单一主题展开，段落之间过渡是否自然、逻辑连贯，有助于读者逐步跟进论证和分析。",
    "weight": 0.15
  }},
  {{
    "criterion": "数据呈现的清晰性与准确性",
    "explanation": "评估报告中引用的数据（如房价、租金、交易量）是否清晰、准确，来源是否标明（如果适用），易于读者查找和理解。",
    "weight": 0.15
  }},
  {{
    "criterion": "图表使用的有效性与规范性",
    "explanation": "评估是否恰当使用图表（如趋势图、对比表）来可视化数据和关系，图表本身是否清晰、规范、易于解读。",
    "weight": 0.10
  }},
  {{
    "criterion": "关键信息的突出与总结",
    "explanation": "评估文章是否通过加粗、小结、摘要等方式有效突出了核心观点、预测结论和关键建议，便于读者快速把握要点。",
    "weight": 0.10
  }},
  {{
    "criterion": "格式排版的整洁性与规范性",
    "explanation": "评估文章的整体排版是否整洁、段间距、字体字号是否适宜，是否有助于减轻阅读疲劳，提升阅读体验。",
    "weight": 0.05
  }},
  {{
    "criterion": "内容表达的读者适应性",
    "explanation": "评估文章在表达方式和术语解释上是否考虑到目标读者的可能背景，避免过于晦涩或过于浅显。",
    "weight": 0.05
  }}
]
</json_output>
</output>
</example>

请严格遵循以上说明和方法，现在针对以下具体任务开始你的工作：
<task>
"{task_prompt}"
</task>
请输出你的 `<analysis>` 和 `<json_output>`。
</user_prompt>
"""