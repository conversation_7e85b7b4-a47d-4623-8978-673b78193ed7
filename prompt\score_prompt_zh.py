# 根据给定的调研任务、合并后的评判标准列表，对两篇文章进行逐条标准的比较评分
generate_merged_score_prompt = """
<system_role>你是一名严格、细致、客观的调研文章评估专家。你擅长根据具体的评估标准，深入比较两篇针对同一任务的文章，并给出精确的评分和清晰的理由。</system_role>

<user_prompt>
**任务背景**
有一个深度调研任务，你需要评估针对该任务撰写的两篇调研文章。我们会从以下四个维度评估文章：全面性、洞察力、指令遵循能力和可读性。内容如下：
<task>
"{task_prompt}"
</task>

**待评估文章**
<article_1>
"{article_1}"
</article_1>

<article_2>
"{article_2}"
</article_2>

**评估标准**
现在，你需要根据以下**评判标准列表**，逐条评估并比较这两篇文章的表现，输出对比分析，然后给出0-10的分数。每个标准都附有其解释，请仔细理解。

<criteria_list>
{criteria_list}
</criteria_list>

<Instruction>
**你的任务**
请严格按照 `<criteria_list>` 中的**每一条标准**，对比评估 `<article_1>` 和 `<article_2>` 在该标准上的具体表现。你需要：
1.  **逐条分析**：针对列表中的每一条标准，分别思考两篇文章是如何满足该标准要求的。
2.  **对比评估**：结合文章内容与标准解释，对比分析两篇文章在每一条标准上的表现。
3.  **分别打分**：基于你的对比分析，为两篇文章在该条标准上的表现分别打分（0-10分）。

**打分规则**
对每一条标准，分别为两篇文章打分，打分范围为 0-10 分（连续的数值）。分数高低应体现文章在该标准上表现的好坏：
*   0-2分：表现很差。几乎完全不符合标准要求。
*   2-4分：表现较差。少量符合标准要求，但有明显不足。
*   4-6分：表现中等。基本符合标准要求，不好不坏。
*   6-8分：表现较好。大部分符合标准要求，有可取之处。
*   8-10分：表现出色/极好。完全或超预期符合标准要求。

**输出格式要求**
请**严格**按照下列`<output_format>`格式输出每一条标准的评估结果，**不要包含任何其他无关内容、引言或总结**。从"标准1"开始，按顺序输出所有标准的评估：
</Instruction>

<output_format>
{{
    "comprehensiveness": [
        {{
            "criterion": [全面性维度的第一条评判标准文本内容],
            "analysis": [对比分析],
            "article_1_score": [0-10连续分数],
            "article_2_score": [0-10连续分数]
        }},
        {{
            "criterion": [全面性维度的第二条评判标准文本内容],
            "analysis": [对比分析],
            "article_1_score": [0-10连续分数],
            "article_2_score": [0-10连续分数]
        }},
        ...
    ],
    "insight": [
        {{
            "criterion": [洞察力维度的第一条评判标准文本内容],
            "analysis": [对比分析],
            "article_1_score": [0-10连续分数],
            "article_2_score": [0-10连续分数]
        }},
        ...
    ],
    ...
}}
</output_format>

现在，请根据调研任务和标准，对两篇文章进行评估，并按照上述要求给出详细的对比分析和评分，请确保输出格式遵守上述`<output_format>`，而且保证其中的json格式可以解析，注意所有可能导致json解析错误的要转义的符号。
</user_prompt>
"""

# 静态版本：使用预定义的固定评判标准，不需要动态生成标准
generate_static_score_prompt = """
<system_role>你是一名严格、细致、客观的调研文章评估专家。你擅长根据具体的评估标准，深入比较两篇针对同一任务的文章，并给出精确的评分和清晰的理由。</system_role>

<user_prompt>
**任务背景**
有一个深度调研任务，你需要评估针对该任务撰写的两篇调研文章。我们会从以下四个维度评估文章：全面性、洞察力、指令遵循能力和可读性。内容如下：
<task>
"{task_prompt}"
</task>

**待评估文章**
<article_1>
"{article_1}"
</article_1>

<article_2>
"{article_2}"
</article_2>

**评估标准**
现在，你需要根据以下**固定的评判标准列表**，逐条评估并比较这两篇文章的表现，输出对比分析，然后给出0-10的分数。每个标准都附有其解释，请仔细理解。

<criteria_list>
# 全面性（Comprehensiveness）
[
  {{
    "criterion": "信息覆盖广度",
    "explanation": "评估文章是否覆盖了与主题相关的所有关键领域和方面，不遗漏重要信息。",
    "weight": 0.25
  }},
  {{
    "criterion": "信息深度与细节",
    "explanation": "评估文章是否提供了足够深入的细节信息，而不只是浅层次的概述。",
    "weight": 0.25
  }},
  {{
    "criterion": "数据与事实支持",
    "explanation": "评估文章是否提供了充分的数据、事实、案例或证据来支持其论点和分析。",
    "weight": 0.25
  }},
  {{
    "criterion": "多角度与平衡性",
    "explanation": "评估文章是否从多个角度考虑问题，并在相关情况下提供平衡的观点。",
    "weight": 0.25
  }}
]

# 洞察力（Insight）
[
  {{
    "criterion": "分析深度与原创性",
    "explanation": "评估文章是否提供了深入的分析和原创性的见解，而非简单重复已知信息。",
    "weight": 0.25
  }},
  {{
    "criterion": "逻辑推理与因果关系",
    "explanation": "评估文章是否展示了清晰的逻辑推理，有效解释了现象背后的因果关系。",
    "weight": 0.25
  }},
  {{
    "criterion": "问题洞察与解决方案",
    "explanation": "评估文章是否识别出关键问题或挑战，并提供了有见地的解决方案或建议。",
    "weight": 0.25
  }},
  {{
    "criterion": "前瞻性与启发性",
    "explanation": "评估文章是否具有前瞻性思考，能够预见趋势并提供启发性的观点。",
    "weight": 0.25
  }}
]

# 指令遵循能力（Instruction Following）
[
  {{
    "criterion": "任务目标的回应",
    "explanation": "评估文章是否直接回应了任务的核心目标和问题。",
    "weight": 0.34
  }},
  {{
    "criterion": "范围限定的遵守",
    "explanation": "评估文章是否严格遵守了任务中设定的范围限定（如地域、时间、对象等）。",
    "weight": 0.33
  }},
  {{
    "criterion": "任务要求的完整覆盖",
    "explanation": "评估文章是否完整覆盖了任务中提出的所有子问题或方面，没有遗漏重要部分。",
    "weight": 0.33
  }}
]

# 可读性（Readability）
[
  {{
    "criterion": "结构清晰与逻辑",
    "explanation": "评估文章是否有清晰的结构，包括合适的引言、主体、结论，以及逻辑上连贯的段落组织。",
    "weight": 0.25
  }},
  {{
    "criterion": "语言表达与流畅性",
    "explanation": "评估文章的语言是否清晰、准确、流畅，没有明显的语法错误或表达不当。",
    "weight": 0.25
  }},
  {{
    "criterion": "专业术语的恰当使用",
    "explanation": "评估文章是否恰当使用专业术语，并在必要时提供解释，便于理解。",
    "weight": 0.25
  }},
  {{
    "criterion": "信息呈现与视觉效果",
    "explanation": "评估文章是否有效使用格式、标题、列表、强调等手段来增强可读性，以及是否合理使用图表或其他视觉元素（如有）。",
    "weight": 0.25
  }}
]
</criteria_list>

<Instruction>
**你的任务**
请严格按照 `<criteria_list>` 中的**每一条标准**，对比评估 `<article_1>` 和 `<article_2>` 在该标准上的具体表现。你需要：
1.  **逐条分析**：针对列表中的每一条标准，分别思考两篇文章是如何满足该标准要求的。
2.  **对比评估**：结合文章内容与标准解释，对比分析两篇文章在每一条标准上的表现。
3.  **分别打分**：基于你的对比分析，为两篇文章在该条标准上的表现分别打分（0-10分）。

**打分规则**
对每一条标准，分别为两篇文章打分，打分范围为 0-10 分（连续的数值）。分数高低应体现文章在该标准上表现的好坏：
*   0-2分：表现很差。几乎完全不符合标准要求。
*   2-4分：表现较差。少量符合标准要求，但有明显不足。
*   4-6分：表现中等。基本符合标准要求，不好不坏。
*   6-8分：表现较好。大部分符合标准要求，有可取之处。
*   8-10分：表现出色/极好。完全或超预期符合标准要求。

**输出格式要求**
请**严格**按照下列`<output_format>`格式输出每一条标准的评估结果，**不要包含任何其他无关内容、引言或总结**。从"标准1"开始，按顺序输出所有标准的评估：
</Instruction>

<output_format>
{{
    "comprehensiveness": [
        {{
            "criterion": [全面性维度的第一条评判标准文本内容],
            "analysis": [对比分析],
            "article_1_score": [0-10连续分数],
            "article_2_score": [0-10连续分数]
        }},
        {{
            "criterion": [全面性维度的第二条评判标准文本内容],
            "analysis": [对比分析],
            "article_1_score": [0-10连续分数],
            "article_2_score": [0-10连续分数]
        }},
        ...
    ],
    "insight": [
        {{
            "criterion": [洞察力维度的第一条评判标准文本内容],
            "analysis": [对比分析],
            "article_1_score": [0-10连续分数],
            "article_2_score": [0-10连续分数]
        }},
        ...
    ],
    ...
}}
</output_format>

现在，请根据调研任务和标准，对两篇文章进行评估，并按照上述要求给出详细的对比分析和评分，请确保输出格式遵守上述`<output_format>`，而且保证其中的json格式可以解析，注意所有可能导致json解析错误的要转义的符号。
</user_prompt>
"""

# 动态但没有参考文章的评分提示版本
point_wise_score_prompt = """
<system_role>你是一名严格、细致、客观的调研文章评估专家。你擅长根据具体的评估标准，对调研文章进行深入评估，并给出精确的评分和清晰的理由。</system_role>

<user_prompt>
**任务背景**
有一个深度调研任务，你需要评估针对该任务撰写的一篇调研文章。我们会从以下四个维度评估文章：全面性、洞察力、指令遵循能力和可读性。内容如下：
<task>
"{task_prompt}"
</task>

**待评估文章**
<target_article>
"{article}"
</target_article>

**评估标准**
现在，你需要根据以下**评判标准列表**，逐条评估这篇文章的表现，输出分析，然后给出0-10的分数。每个标准都附有其解释，请仔细理解。

<criteria_list>
{criteria_list}
</criteria_list>

<Instruction>
**你的任务**
请严格按照 `<criteria_list>` 中的**每一条标准**，评估 `<target_article>` 在该标准上的具体表现。你需要：
1.  **逐条分析**：针对列表中的每一条标准，思考文章是如何满足该标准要求的。
2.  **分析评估**：结合文章内容与标准解释，分析文章在每一条标准上的表现，指出其优点和不足。
3.  **打分**：基于你的分析，为文章在该条标准上的表现打分（0-10分）。

**打分规则**
对每一条标准，为文章打分，打分范围为 0-10 分（连续的数值）。分数高低应体现文章在该标准上表现的好坏：
*   0-2分：表现很差。几乎完全不符合标准要求。
*   2-4分：表现较差。少量符合标准要求，但有明显不足。
*   4-6分：表现中等。基本符合标准要求，不好不坏。
*   6-8分：表现较好。大部分符合标准要求，有可取之处。
*   8-10分：表现出色/极好。完全或超预期符合标准要求。

**输出格式要求**
请**严格**按照下列`<output_format>`格式输出每一条标准的评估结果，**不要包含任何其他无关内容、引言或总结**。从"标准1"开始，按顺序输出所有标准的评估：
</Instruction>

<output_format>
{{
    "comprehensiveness": [
        {{
            "criterion": [全面性维度的第一条评判标准文本内容],
            "analysis": [分析],
            "target_score": [0-10连续分数]
        }},
        {{
            "criterion": [全面性维度的第二条评判标准文本内容],
            "analysis": [分析],
            "target_score": [0-10连续分数]
        }},
        ...
    ],
    "insight": [
        {{
            "criterion": [洞察力维度的第一条评判标准文本内容],
            "analysis": [分析],
            "target_score": [0-10连续分数]
        }},
        ...
    ],
    ...
}}
</output_format>

现在，请根据调研任务和标准，对文章进行评估，并按照上述要求给出详细的分析和评分，请确保输出格式遵守上述`<output_format>`，而且保证其中的json格式可以解析，注意所有可能导致json解析错误的要转义的符号。
</user_prompt>
"""

# Vanilla Prompt: 直接要求模型对文章进行整体评分
vanilla_prompt = """
<system_role>你是一名严格、细致、客观的调研文章评估专家。你擅长根据具体的评估标准，对调研文章进行深入评估，并给出精确的评分和清晰的理由。</system_role>

<user_prompt>
**任务背景**
有一个深度调研任务，你需要评估针对该任务撰写的一篇调研文章。
<task>
"{task_prompt}"
</task>

**待评估文章**
<target_article>
"{article}"
</target_article>

<Instruction>
**你的任务**
请评估以上 `<target_article>` 作为对 `<task>` 的回应，其整体质量如何。

请给出一个介于0到10之间的整体分数。同时，请提供简短的评分理由。

**输出格式要求**
请**严格**按照下列`<output_format>`格式输出你的评估结果，**不要包含任何其他无关内容、引言或总结**。
</Instruction>

<output_format>
{{
    "overall_score": [0-10连续分数],
    "justification": "[评分理由]"
}}
</output_format>

现在，请根据任务评估文章，并按照指定格式提供分数和理由。请确保输出为有效的JSON格式，并注意转义特殊字符。
</user_prompt>
"""


    