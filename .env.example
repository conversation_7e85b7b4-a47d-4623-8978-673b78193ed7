# API Provider Configuration
# Set to "true" to use third-party OpenAI-compatible API, "false" to use Google API
USE_THIRD_PARTY_API=true

# Third-party API Configuration (when USE_THIRD_PARTY_API=true)
THIRD_PARTY_BASE_URL=https://api.openai.com/v1
THIRD_PARTY_API_KEY=your_third_party_api_key_here
THIRD_PARTY_MODEL=gemini-2.5-pro
THIRD_PARTY_FACT_MODEL=gemini-2.5-flash

# Google API Configuration (when USE_THIRD_PARTY_API=false)
GEMINI_API_KEY=your_google_gemini_api_key_here

# Jina API for web scraping (required for FACT evaluation)
JINA_API_KEY=your_jina_api_key_here

# Example configurations for different providers:

# For OpenAI-compatible services:
# THIRD_PARTY_BASE_URL=https://api.openai.com/v1
# THIRD_PARTY_MODEL=gpt-4o
# THIRD_PARTY_FACT_MODEL=gpt-4o-mini

# For other third-party Gemini providers:
# THIRD_PARTY_BASE_URL=https://your-provider.com/v1
# THIRD_PARTY_MODEL=gemini-2.5-pro
# THIRD_PARTY_FACT_MODEL=gemini-2.5-flash
