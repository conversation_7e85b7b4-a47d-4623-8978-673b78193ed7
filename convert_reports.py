#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
转换研究报告MD文件为JSONL格式的脚本
用于DeepResearch Bench评估
"""

import os
import json
import re
from pathlib import Path

# 配置
REPORTS_DIR = r"D:\Desktop\ellchan\研究报告"  # 您的报告目录
OUTPUT_DIR = "data/test_data/raw_data"  # 输出目录
MODEL_NAME = "deepresearch-kimik2"  # 模型名称
QUERY_FILE = "data/prompt_data/query.jsonl"  # 查询文件

def load_queries():
    """加载查询数据"""
    queries = {}
    try:
        with open(QUERY_FILE, 'r', encoding='utf-8') as f:
            for line in f:
                if line.strip():
                    data = json.loads(line.strip())
                    queries[data['id']] = data['prompt']
        print(f"成功加载 {len(queries)} 个查询")
        return queries
    except Exception as e:
        print(f"加载查询文件失败: {e}")
        return {}

def extract_id_from_filename(filename):
    """从文件名提取ID"""
    # 匹配数字.md格式
    match = re.match(r'(\d+)\.md$', filename)
    if match:
        return int(match.group(1))
    return None

def read_md_file(filepath):
    """读取MD文件内容"""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
        return content.strip()
    except Exception as e:
        print(f"读取文件 {filepath} 失败: {e}")
        return None

def convert_reports():
    """转换报告文件"""
    # 加载查询数据
    queries = load_queries()
    if not queries:
        print("无法加载查询数据，退出")
        return
    
    # 确保输出目录存在
    os.makedirs(OUTPUT_DIR, exist_ok=True)
    
    # 扫描报告目录
    reports_path = Path(REPORTS_DIR)
    if not reports_path.exists():
        print(f"报告目录不存在: {REPORTS_DIR}")
        return
    
    # 收集所有MD文件
    md_files = list(reports_path.glob("*.md"))
    print(f"找到 {len(md_files)} 个MD文件")
    
    # 转换数据
    converted_data = []
    failed_files = []
    
    for md_file in md_files:
        # 提取ID
        file_id = extract_id_from_filename(md_file.name)
        if file_id is None:
            print(f"无法从文件名提取ID: {md_file.name}")
            failed_files.append(md_file.name)
            continue
        
        # 检查ID是否在查询中
        if file_id not in queries:
            print(f"ID {file_id} 不在查询列表中")
            failed_files.append(md_file.name)
            continue
        
        # 读取文件内容
        content = read_md_file(md_file)
        if content is None:
            failed_files.append(md_file.name)
            continue
        
        # 创建JSON对象
        json_obj = {
            "id": file_id,
            "prompt": queries[file_id],
            "article": content
        }
        
        converted_data.append(json_obj)
        print(f"成功转换: {md_file.name} (ID: {file_id})")
    
    # 按ID排序
    converted_data.sort(key=lambda x: x['id'])
    
    # 保存JSONL文件
    output_file = os.path.join(OUTPUT_DIR, f"{MODEL_NAME}.jsonl")
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            for item in converted_data:
                f.write(json.dumps(item, ensure_ascii=False) + '\n')
        
        print(f"\n转换完成!")
        print(f"成功转换: {len(converted_data)} 个文件")
        print(f"失败文件: {len(failed_files)} 个")
        if failed_files:
            print(f"失败文件列表: {failed_files}")
        print(f"输出文件: {output_file}")
        
        # 显示转换的ID列表
        ids = [item['id'] for item in converted_data]
        print(f"转换的ID列表: {sorted(ids)}")
        
    except Exception as e:
        print(f"保存文件失败: {e}")

def main():
    """主函数"""
    print("DeepResearch Bench 报告转换脚本")
    print("=" * 50)
    print(f"报告目录: {REPORTS_DIR}")
    print(f"输出目录: {OUTPUT_DIR}")
    print(f"模型名称: {MODEL_NAME}")
    print("=" * 50)
    
    convert_reports()

if __name__ == "__main__":
    main()
