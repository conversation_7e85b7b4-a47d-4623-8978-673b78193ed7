import os
from typing import Optional, Dict, Any
import requests
import logging
import json

# Try to import Google genai, fallback to None if not available
try:
    from google import genai
    from google.genai import types
    GOOGLE_GENAI_AVAILABLE = True
except ImportError:
    GOOGLE_GENAI_AVAILABLE = False
    genai = None
    types = None

logging.basicConfig(level=logging.WARNING)
logger = logging.getLogger(__name__)

if GOOGLE_GENAI_AVAILABLE:
    logging.getLogger('google').setLevel(logging.WARNING)
    logging.getLogger('google.genai').setLevel(logging.WARNING)
logging.getLogger('httpx').setLevel(logging.WARNING)

# Configuration for API provider
# Set USE_THIRD_PARTY_API=true to use third-party OpenAI-compatible API
USE_THIRD_PARTY_API = os.environ.get("USE_THIRD_PARTY_API", "false").lower() == "true"

# Third-party API configuration
THIRD_PARTY_BASE_URL = os.environ.get("THIRD_PARTY_BASE_URL", "https://api.openai.com/v1")
THIRD_PARTY_API_KEY = os.environ.get("THIRD_PARTY_API_KEY", "")
THIRD_PARTY_MODEL = os.environ.get("THIRD_PARTY_MODEL", "gemini-2.5-pro")
THIRD_PARTY_FACT_MODEL = os.environ.get("THIRD_PARTY_FACT_MODEL", "gemini-2.5-flash")

# Google API configuration (fallback)
GOOGLE_API_KEY = os.environ.get("GEMINI_API_KEY", "")
GOOGLE_MODEL = "gemini-2.5-pro-preview-06-05"
GOOGLE_FACT_MODEL = "gemini-2.5-flash-preview-05-20"

# Jina API for web scraping
READ_API_KEY = os.environ.get("JINA_API_KEY", "")

# Set default values based on provider
if USE_THIRD_PARTY_API:
    API_KEY = THIRD_PARTY_API_KEY
    Model = THIRD_PARTY_MODEL
    FACT_Model = THIRD_PARTY_FACT_MODEL
else:
    API_KEY = GOOGLE_API_KEY
    Model = GOOGLE_MODEL
    FACT_Model = GOOGLE_FACT_MODEL

class AIClient:

    def __init__(self, api_key=API_KEY, model=Model):
        self.use_third_party = USE_THIRD_PARTY_API
        self.model = model

        if self.use_third_party:
            # Third-party API configuration
            self.api_key = api_key or THIRD_PARTY_API_KEY
            self.base_url = THIRD_PARTY_BASE_URL
            if not self.api_key:
                raise ValueError("Third-party API key not provided! Please set THIRD_PARTY_API_KEY environment variable.")
        else:
            # Google API configuration
            self.api_key = api_key or GOOGLE_API_KEY
            if not self.api_key:
                raise ValueError("Gemini API key not provided! Please set GEMINI_API_KEY environment variable.")
            if not GOOGLE_GENAI_AVAILABLE:
                raise ValueError("Google genai library not available! Please install google-genai or use third-party API.")
            self.client = genai.Client(api_key=self.api_key, http_options={'timeout': 600000})

    def generate(self, user_prompt: str, system_prompt: str = "", model: Optional[str] = None) -> str:
        """
        Generate text response using either Google API or third-party OpenAI-compatible API
        """
        model_to_use = model or self.model

        if self.use_third_party:
            return self._generate_third_party(user_prompt, system_prompt, model_to_use)
        else:
            return self._generate_google(user_prompt, system_prompt, model_to_use)

    def _generate_third_party(self, user_prompt: str, system_prompt: str, model: str) -> str:
        """
        Generate using third-party OpenAI-compatible API
        """
        # 清理和规范化文本，处理特殊Unicode字符
        def clean_text(text):
            if not text:
                return text
            # 替换常见的特殊Unicode字符
            text = text.replace('\u2011', '-')  # 非断连字符
            text = text.replace('\u2012', '-')  # 数字破折号
            text = text.replace('\u2013', '-')  # en dash
            text = text.replace('\u2014', '--') # em dash
            text = text.replace('\u2015', '--') # 水平线
            text = text.replace('\u2018', "'")  # 左单引号
            text = text.replace('\u2019', "'")  # 右单引号
            text = text.replace('\u201c', '"')  # 左双引号
            text = text.replace('\u201d', '"')  # 右双引号
            # 确保文本是UTF-8编码
            if isinstance(text, str):
                text = text.encode('utf-8', errors='ignore').decode('utf-8')
            return text

        # 清理输入文本
        user_prompt = clean_text(user_prompt)
        system_prompt = clean_text(system_prompt)

        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json; charset=utf-8"
        }

        messages = []
        if system_prompt:
            messages.append({"role": "system", "content": system_prompt})
        messages.append({"role": "user", "content": user_prompt})

        data = {
            "model": model,
            "messages": messages,
            "temperature": 0.1,
            "max_tokens": 4096
        }

        try:
            response = requests.post(
                f"{self.base_url}/chat/completions",
                headers=headers,
                json=data,
                timeout=600
            )
            response.raise_for_status()

            result = response.json()
            return result["choices"][0]["message"]["content"]

        except Exception as e:
            raise Exception(f"Failed to generate content with third-party API: {str(e)}")

    def _generate_google(self, user_prompt: str, system_prompt: str, model: str) -> str:
        """
        Generate using Google Gemini API
        """
        # 清理和规范化文本，处理特殊Unicode字符
        def clean_text(text):
            if not text:
                return text
            # 替换常见的特殊Unicode字符
            text = text.replace('\u2011', '-')  # 非断连字符
            text = text.replace('\u2012', '-')  # 数字破折号
            text = text.replace('\u2013', '-')  # en dash
            text = text.replace('\u2014', '--') # em dash
            text = text.replace('\u2015', '--') # 水平线
            text = text.replace('\u2018', "'")  # 左单引号
            text = text.replace('\u2019', "'")  # 右单引号
            text = text.replace('\u201c', '"')  # 左双引号
            text = text.replace('\u201d', '"')  # 右双引号
            # 确保文本是UTF-8编码
            if isinstance(text, str):
                text = text.encode('utf-8', errors='ignore').decode('utf-8')
            return text

        # 清理输入文本
        user_prompt = clean_text(user_prompt)
        system_prompt = clean_text(system_prompt)

        # Build request content
        contents = []

        # Add system prompt
        if system_prompt:
            contents.append({
                "role": "system",
                "parts": [{"text": system_prompt}]
            })

        # Add user prompt
        contents.append({
            "role": "user",
            "parts": [{"text": user_prompt}]
        })

        try:
            response = self.client.models.generate_content(
                model=model,
                contents=contents,
                config=types.GenerateContentConfig(
                    thinking_config=types.ThinkingConfig(thinking_budget=16000)
                )
            )

            return response.text

        except Exception as e:
            raise Exception(f"Failed to generate content with Google API: {str(e)}")

class WebScrapingJinaTool:
    def __init__(self, api_key: str = None):
        self.api_key = api_key or os.environ.get("JINA_API_KEY")
        if not self.api_key:
            raise ValueError("Jina API key not provided! Please set JINA_API_KEY environment variable.")

    def __call__(self, url: str) -> Dict[str, Any]:
        try:
            jina_url = f'https://r.jina.ai/{url}'
            headers = {
                "Accept": "application/json",
                'Authorization': self.api_key,
                'X-Timeout': "60000",
                "X-With-Generated-Alt": "true",
            }
            response = requests.get(jina_url, headers=headers)

            if response.status_code != 200:
                raise Exception(f"Jina AI Reader Failed for {url}: {response.status_code}")

            response_dict = response.json()

            return {
                'url': response_dict['data']['url'],
                'title': response_dict['data']['title'],
                'description': response_dict['data']['description'],
                'content': response_dict['data']['content'],
                'publish_time': response_dict['data'].get('publishedTime', 'unknown')
            }

        except Exception as e:
            logger.error(str(e))
            return {
                'url': url,
                'content': '',
                'error': str(e)
            }
        
# 延迟初始化Jina工具，避免在只使用RACE评估时报错
jina_tool = None

def scrape_url(url: str) -> Dict[str, Any]:
    global jina_tool
    if jina_tool is None:
        jina_tool = WebScrapingJinaTool()
    return jina_tool(url)
    
def call_model(user_prompt: str) -> str:
    client = AIClient(model=FACT_Model)
    return client.generate(user_prompt)

if __name__ == "__main__":
    url = ""
    result = scrape_url(url)
    print(result)